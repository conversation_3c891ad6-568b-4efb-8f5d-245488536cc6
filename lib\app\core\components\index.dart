// Nexed Mini Component Library
// This file provides a centralized export for all reusable components

// === UNIFIED COMPONENT SYSTEM ===

// Buttons
export '../widgets/unified_button.dart';
export '../widgets/unified_icon_button.dart';

// Cards
export '../widgets/unified_card.dart';

// Theme System
export '../theme/app_theme.dart';

// === LEGACY COMPONENTS (Deprecated) ===
// These are kept for backward compatibility but should be replaced with unified components

// Legacy Buttons (use UnifiedButton instead)
export '../widgets/buttons/index.dart';

// Legacy Cards (use UnifiedCard instead)
export '../widgets/cards/cards.dart';

// Other Widgets
export '../widgets/index.dart';

// === COMPONENT USAGE GUIDE ===
/*

## Unified Button System

### Basic Usage:
```dart
// Filled button (default)
UnifiedButton.filled(
  text: 'Submit',
  onPressed: () {},
)

// Outlined button
UnifiedButton.outlined(
  text: 'Cancel',
  onPressed: () {},
)

// Text button
UnifiedButton.text(
  text: 'Skip',
  onPressed: () {},
)

// Gradient button
UnifiedButton.gradient(
  text: 'Get Started',
  onPressed: () {},
)

// With icon
UnifiedButton.filled(
  text: 'Save',
  icon: Icons.save,
  onPressed: () {},
)

// Different sizes
UnifiedButton.filled(
  text: 'Small',
  size: ButtonSize.small,
  onPressed: () {},
)
```

### Icon Buttons:
```dart
// Filled icon button
UnifiedIconButton.filled(
  icon: Icons.add,
  onPressed: () {},
)

// Outlined icon button
UnifiedIconButton.outlined(
  icon: Icons.edit,
  onPressed: () {},
)
```

## Unified Card System

### Basic Usage:
```dart
// Elevated card (Material 3 style)
UnifiedCard.elevated(
  child: Text('Content'),
)

// Filled card
UnifiedCard.filled(
  child: Text('Content'),
)

// Outlined card
UnifiedCard.outlined(
  child: Text('Content'),
)

// Flat card
UnifiedCard.flat(
  child: Text('Content'),
)

// Different sizes
UnifiedCard.elevated(
  size: CardSize.large,
  child: Text('Large card'),
)

// With tap handler
UnifiedCard.elevated(
  onTap: () {},
  child: Text('Tappable card'),
)
```

## Theme System

### Optimized Theme Access:
```dart
// Instead of multiple Theme.of(context) calls:
Widget build(BuildContext context) {
  final theme = Theme.of(context);
  final colors = AppColors.getThemeColors(context);
  
  return Container(
    color: colors.textPrimary,
    child: Text(
      'Hello',
      style: theme.textTheme.bodyMedium?.copyWith(
        color: colors.textSecondary,
      ),
    ),
  );
}
```

## Migration Guide

### From Legacy Buttons:
```dart
// OLD: FlatButton
FlatButton(
  text: 'Submit',
  onPressed: () {},
  outlined: true,
)

// NEW: UnifiedButton
UnifiedButton.outlined(
  text: 'Submit',
  onPressed: () {},
)
```

### From Legacy Cards:
```dart
// OLD: BaseCard
BaseCard(
  elevation: 4,
  child: Text('Content'),
)

// NEW: UnifiedCard
UnifiedCard.elevated(
  elevation: 4,
  child: Text('Content'),
)
```

*/
